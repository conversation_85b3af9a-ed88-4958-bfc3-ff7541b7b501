<!DOCTYPE html>
<html lang="cs">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Adresář OTE">
    <meta name="author" content="<PERSON>">
    <link rel="shortcut icon" href="img/favicon.ico" type="image/x-icon">
    <title><PERSON>resář OTE</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@200;300;400;600;700;800;900&display=swap"
        rel="stylesheet">
    <script src="https://kit.fontawesome.com/14940b9e28.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/directory.css">
    <link rel="stylesheet" href="css/map-integration.css">
    <style>
        /* Navigační tlačítka */
        .navigation-buttons {
            display: flex;
            gap: 0.5rem;
            margin: 0 1rem;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            text-decoration: none;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .nav-btn.active {
            background: rgba(255, 255, 255, 0.95);
            border-color: rgba(255, 255, 255, 0.8);
            color: #2563eb;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-btn i {
            font-size: 1.1rem;
        }

        
        @media (max-width: 768px) {
            .navigation-buttons {
                margin: 0 0.5rem;
                gap: 0.25rem;
            }

            .nav-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.8rem;
            }

            .nav-btn span {
                display: none;
            }

            .nav-btn i {
                font-size: 1.2rem;
            }
        }

        /* Mapa sekce styly */
        .office_map_section {
            padding: 2rem 0;
            background: #f7f9fb;
            min-height: calc(100vh - 200px);
        }

        /* Přechody mezi sekcemi */
        .section-transition {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .section-transition.active {
            opacity: 1;
            transform: translateY(0);
        }

        /* Styly z mapa.html */
        body {
            font-family: 'Nunito', Arial, sans-serif;
        }

        /* Styly pro boční panel oddělení - sjednocené pro directory i mapu */
        .employee-directory-container {
            display: block;
            position: relative;
        }

        .departments-panel-directory,
        .departments-panel {
            position: fixed;
            left: 0;
            top: 80px;
            width: 280px;
            height: calc(100vh - 80px);
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-right: 2px solid #e5e7eb;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            z-index: 10;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .departments-panel-directory .panel-header,
        .departments-panel .panel-header {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            padding: 1.5rem;
            border-bottom: 2px solid #1d4ed8;
        }

        .departments-panel-directory .panel-header h3,
        .departments-panel .panel-header h3 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .departments-list {
            padding: 1rem;
            height: calc(100vh - 160px);
            overflow-y: auto;
        }

        .department-item {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .department-item:hover {
            background: #f1f5f9;
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .department-item.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            border-color: #1d4ed8;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
        }

        .department-name {
            font-weight: 600;
            font-size: 0.95rem;
            margin-bottom: 0.25rem;
        }

        .department-count {
            font-size: 0.85rem;
            opacity: 0.8;
        }

        .employee-content {
            margin-left: 280px;
            padding: 0 2rem;
            min-width: 0;
        }

        /* Dark mode pro levý panel - sjednocené */
        .dark-mode .departments-panel-directory,
        .dark-mode .departments-panel {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border-right-color: #475569;
        }

        .dark-mode .departments-panel-directory .panel-header,
        .dark-mode .departments-panel .panel-header {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            border-bottom-color: #334155;
        }

        .dark-mode .department-item {
            background: #334155;
            border-color: #475569;
            color: #e2e8f0;
        }

        .dark-mode .department-item:hover {
            background: #475569;
            border-color: #3b82f6;
        }

        .dark-mode .department-item.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: #ffffff;
            border-color: #1d4ed8;
        }

        /* Responzivní design */
        @media (max-width: 768px) {
            .departments-panel-directory {
                width: 100%;
                height: auto;
                position: relative;
                top: 0;
                left: 0;
            }

            .employee-content {
                margin-left: 0;
                padding: 0 1rem;
            }

            .departments-list {
                height: auto;
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                padding: 1rem;
            }

            .department-item {
                margin-bottom: 0;
                flex: 1;
                min-width: 150px;
            }
        }

        .main-flex {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: flex-start;
            width: 100vw;
            max-width: none;
            margin: 2rem 0 2rem 0;
            gap: 48px;
        }

        /* Styly pro zaměstnance - bez hierarchického řazení */
        .employee.manager {
            position: relative;
            /* Odstraněno: order: -1; - všichni zaměstnanci budou řazeni abecedně */
        }

        .hierarchy-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            z-index: 5;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .hierarchy-badge.predstavenstvo {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .hierarchy-badge.vedouci {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
        }

        .hierarchy-badge i {
            font-size: 0.6rem;
        }

        .dark-mode .hierarchy-badge {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }

        .dark-mode .hierarchy-badge.predstavenstvo {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }

        /* Vylepšené modální okno */
        .modal-content {
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modal-content h2 {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .modal-badge {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
        }

        .modal-info {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 173, 208, 0.2);
        }

        .modal-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
            justify-content: center;
        }

        .modal-action-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal-action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .modal-action-btn:hover::before {
            left: 100%;
        }

        .modal-action-btn.map-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .modal-action-btn.map-btn:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
            transform: translateY(-2px);
        }

        .main-flex-custom {
            flex-direction: column;
            gap: 32px;
            align-items: stretch;
        }

        .employee-bar {
            width: 100%;
            margin: 0 0 1.5rem 0;
            background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
            box-shadow:
                0 8px 24px rgba(37,99,235,0.06),
                0 4px 8px rgba(37,99,235,0.04),
                inset 0 1px 0 rgba(255,255,255,0.8);
            border-radius: 20px;
            border: 1px solid rgba(37,99,235,0.1);
            padding: 1.25rem 1.5rem;
            position: relative;
            z-index: 10;
            backdrop-filter: blur(10px);
            height: auto;
            min-height: 120px;
        }

        .search-header {
            display: flex;
            align-items: center;
            gap: 3rem;
            padding: 1rem 1.5rem;
            background: rgba(255,255,255,0.7);
            border-radius: 16px;
            border: 1px solid rgba(37,99,235,0.08);
            backdrop-filter: blur(20px);
            height: 80px;
        }

        .search-section {
            display: flex;
            align-items: center;
            flex-shrink: 0;
            min-width: 220px;
        }

        .search-row {
            display: flex;
            align-items: center;
            gap: 1rem;
            width: 100%;
        }

        .search-left {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex: 1;
        }

        .department-title {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            border-radius: 10px;
            font-size: 0.85rem;
            font-weight: 600;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
            min-width: 180px;
            justify-content: center;
        }

        .department-title i {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .search-container {
            position: relative;
            width: 280px; /* Zvětšeno z 200px na 280px */
            flex-shrink: 0;
        }

        .search-container::before {
            content: '\f002';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            font-size: 0.875rem;
            z-index: 1;
            pointer-events: none;
        }

        .search-box-horizontal {
            width: 100%;
            border-radius: 10px;
            border: 2px solid rgba(37,99,235,0.12);
            padding: 0.5rem 2.25rem 0.5rem 2rem;
            font-size: 0.85rem;
            background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,255,0.95) 100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
            color: #1f2937;
            font-weight: 500;
            box-shadow:
                0 4px 12px rgba(37,99,235,0.08),
                inset 0 1px 0 rgba(255,255,255,0.9);
            backdrop-filter: blur(12px);
            height: 36px;
            position: relative;
        }

        .search-box-horizontal:focus {
            border-color: #2563eb;
            background: linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(248,250,255,1) 100%);
            box-shadow:
                0 8px 32px rgba(37,99,235,0.15),
                0 0 0 3px rgba(37,99,235,0.1),
                inset 0 1px 0 rgba(255,255,255,1);
            transform: translateY(-1px);
        }

        .search-clear-btn {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(107,114,128,0.1);
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            opacity: 0;
            pointer-events: none;
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-clear-btn.visible {
            opacity: 1;
            pointer-events: auto;
        }

        .search-clear-btn:hover {
            color: #dc2626;
            background: rgba(220,38,38,0.1);
            transform: translateY(-50%) scale(1.1);
        }

        .search-results-count {
            display: none;
            font-size: 0.75rem;
            color: #6b7280;
            font-weight: 500;
            padding: 0.25rem 0.5rem;
            background: rgba(37,99,235,0.08);
            border-radius: 8px;
            margin-left: 0.5rem;
            white-space: nowrap;
            transition: all 0.3s ease;
        }

        .search-results-count.visible {
            display: flex;
            align-items: center;
        }

        /* Search hint and not found messages */
        .search-hint, .not-found-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            padding: 1rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 0.95rem;
            z-index: 10001;
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
        }

        .search-hint {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .not-found-message {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .search-hint.show, .not-found-message.show {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }

        .search-hint i, .not-found-message i {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* Dark mode pro název oddělení */
        .dark-mode .department-title {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            border: 1px solid #475569;
            color: #e2e8f0;
        }

        /* Responzivní design pro název oddělení */
        @media (max-width: 768px) {
            .department-title {
                min-width: 120px;
                font-size: 0.75rem;
                padding: 0.4rem 0.8rem;
            }

            .department-title span {
                display: none;
            }

            .department-title::after {
                content: attr(data-short);
            }
        }

        /* Skrytí tlačítka "back to top" ve vnořené mapě */
        body.map-view #backToTopBtn {
            display: none;
        }
    </style>


</head>

<body>

    <header id="headerNav">
        <div class="navbar">
            <div class="logo">
                <a href="index.html"><img src="img/logo1.svg" alt="Logo OTE" class="logo1"></a>
            </div>
            <div class="header-content">
                <h1 class="header-title" id="pageTitle">Adresář OTE</h1>
                <div class="view-toggle-header">
                    <button id="employeeListBtn" class="view-toggle-btn active" data-view="employees" onclick="switchToEmployees()">
                        <i class="fas fa-users"></i>
                        <span>Adresář</span>
                    </button>
                    <button id="officeMapBtn" class="view-toggle-btn" data-view="map" onclick="switchToMap()">
                        <i class="fas fa-map-marked-alt"></i>
                        <span>Mapa</span>
                    </button>
                </div>
            </div>
            <div class="header-actions">
                <button class="theme-toggle-btn" id="themeToggle" title="Přepnout režim">
                    <i class="fas fa-sun" id="themeIcon"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Hlavní vyhledávací sekce -->
    <section class="main-search-section" id="mainSearchSection">
        <div class="search-container-main">
            <div class="search-wrapper">
                <div class="search-input-group">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" id="searchEmployee" placeholder="Vyhledat zaměstnance..." class="main-search-input">
                    <button type="button" class="search-clear-btn" id="searchClearBtn" style="display: none;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="search-suggestions" class="search-suggestions"></div>
            </div>
        </div>
    </section>

    <!-- Adresář OTE -->
    <section class="employee_directory" id="employeeSection">
        <div class="employee-directory-container">
            <!-- Levý panel s oddělením -->
            <div class="departments-panel-directory" id="departmentsPanelDirectory">
                <div class="panel-header">
                    <h3><i class="fas fa-building"></i> Oddělení</h3>
                </div>
                <div class="departments-list" id="departmentsListDirectory">
                    <!-- Oddělení se načtou dynamicky -->
                </div>
            </div>

            <!-- Hlavní obsah s kartami zaměstnanců -->
            <div class="employee-content">
                <div class="filter-header">
                </div>

                <div class="employee_listing" id="employee-grid">
                </div>
            </div>
        </div>
    </section>

    <!-- Mapa rozmístění pracovišť - 1:1 kopie z mapa.html -->
    <section class="office_map_section" id="mapSection" style="display: none;">
        <div class="main-flex main-flex-custom">
            <div class="employee-bar">
                <div class="search-header">
                    <div class="search-section">
                        <div class="search-row">
                            <div class="search-left">
                                <div class="department-title" id="mapDepartmentTitle">
                                    <i class="fas fa-building"></i>
                                    <span id="mapDepartmentName">Všichni zaměstnanci</span>
                                </div>
                                <div class="search-container">
                                    <input class="search-box-horizontal" type="text" id="mapSearchInput" placeholder="Vyhledat zaměstnance...">
                                    <button type="button" class="search-clear-btn" id="mapSearchClearBtn" title="Vymazat vyhledávání">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="search-results-count" id="mapSearchResultsCount">
                                    <span id="mapResultsCountText">0 výsledků</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <ul class="employee-list-horizontal" id="mapEmployeeList"></ul>
                </div>
            </div>

            <div class="mapa-wrapper mapa-wrapper-custom">
                <!-- Levý panel s oddělením -->
                <div class="departments-panel" id="departmentsPanel">
                    <div class="panel-header">
                        <h3><i class="fas fa-building"></i> Oddělení</h3>
                    </div>
                    <div class="departments-list" id="departmentsList">
                        <!-- Dynamicky generované oddělení -->
                    </div>
                </div>

                <div class="office-map-container" id="mapContainer">
                    <img id="office-map-img" src="img/Greenline.png" alt="Plán kanceláře">
                </div>
            </div>
        </div>
        <div id="map-error" class="error-message">Obrázek plánu kanceláře se nepodařilo načíst. Zkontrolujte cestu k souboru <b>img/Greenline.png</b> nebo kontaktujte správce.</div>
    </section>

    <footer id="Footer">
        <p>Poslední aktualizace: <span id="lastUpdated"></span></p>
    </footer>

    <button id="backToTopBtn" title="Zpět nahoru">
        <i class="fas fa-chevron-up"></i>
        <span>Nahoru</span>
    </button>

    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <img id="modalImage" src="" alt="Employee Image">
            <h2 id="modalName"></h2>
            <div class="modal-header">
                <div class="modal-info-group">
                    <p id="modalPosition"></p>
                    <p id="modalDepartment"></p>
                    <p id="modalOffice"></p>
                </div>
            </div>
            <p id="modalDescription"></p>
            <div class="modal-info">
                <div class="contact-item">
                    <i class="fas fa-phone icon"></i>
                    <span id="modalPhone">Telefon: Neuvedeno</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-mobile-alt icon"></i>
                    <span id="modalMobile">Mobil: Neuvedeno</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope icon"></i>
                    <span id="modalEmail">Email: Neuvedeno</span>
                </div>
                <div id="modalTeams"></div>
            </div>
        </div>
    </div>

    <script src="js/navbar.js?v=8"></script>
    <script src="js/script.js?v=8"></script>
    <script src="js/back-to-top.js?v=8"></script>
    <script src="js/update-date.js?v=8"></script>
    <script src="js/map-integration.js?v=8"></script>

    <script>
        document.addEventListener('contextmenu', function (e) {
            e.preventDefault();
        });
    </script>

</body>

</html>
