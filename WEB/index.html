<!DOCTYPE html>
<html lang="cs">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Adresář OTE">
    <meta name="author" content="<PERSON>">
    <link rel="shortcut icon" href="img/favicon.ico" type="image/x-icon">
    <title><PERSON>resář OTE</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@200;300;400;600;700;800;900&display=swap"
        rel="stylesheet">
    <script src="https://kit.fontawesome.com/14940b9e28.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/directory.css">
    <link rel="stylesheet" href="css/map-integration.css">
    <style>
        /* Navigační tlačítka */
        .navigation-buttons {
            display: flex;
            gap: 0.5rem;
            margin: 0 1rem;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.25rem;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            text-decoration: none;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            color: #fff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .nav-btn.active {
            background: rgba(255, 255, 255, 0.95);
            border-color: rgba(255, 255, 255, 0.8);
            color: #2563eb;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .nav-btn i {
            font-size: 1.1rem;
        }

        
        @media (max-width: 768px) {
            .navigation-buttons {
                margin: 0 0.5rem;
                gap: 0.25rem;
            }

            .nav-btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.8rem;
            }

            .nav-btn span {
                display: none;
            }

            .nav-btn i {
                font-size: 1.2rem;
            }
        }

        /* Mapa sekce styly */
        .office_map_section {
            padding: 2rem 0;
            background: #f7f9fb;
            min-height: calc(100vh - 200px);
        }

        /* Přechody mezi sekcemi */
        .section-transition {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .section-transition.active {
            opacity: 1;
            transform: translateY(0);
        }

        /* Styly z mapa.html */
        body {
            font-family: 'Nunito', Arial, sans-serif;
        }

        /* Styly pro boční panel oddělení - sjednocené pro directory i mapu */
        .employee-directory-container {
            display: block;
            position: relative;
        }

        .departments-panel-directory,
        .departments-panel {
            position: fixed;
            left: 0;
            top: 80px;
            width: 280px;
            height: calc(100vh - 80px);
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-right: 2px solid #e5e7eb;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            z-index: 10;
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .departments-panel-directory .panel-header,
        .departments-panel .panel-header {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            padding: 1.5rem;
            border-bottom: 2px solid #1d4ed8;
        }

        .departments-panel-directory .panel-header h3,
        .departments-panel .panel-header h3 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .departments-list {
            padding: 1rem;
            height: calc(100vh - 160px);
            overflow-y: auto;
        }

        .department-item {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .department-item:hover {
            background: #f1f5f9;
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .department-item.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            border-color: #1d4ed8;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
        }

        .department-name {
            font-weight: 600;
            font-size: 0.95rem;
            margin-bottom: 0.25rem;
        }

        .department-count {
            font-size: 0.85rem;
            opacity: 0.8;
        }

        .employee-content {
            margin-left: 280px;
            padding: 0 2rem;
            min-width: 0;
        }

        /* Dark mode pro levý panel - sjednocené */
        .dark-mode .departments-panel-directory,
        .dark-mode .departments-panel {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border-right-color: #475569;
        }

        .dark-mode .departments-panel-directory .panel-header,
        .dark-mode .departments-panel .panel-header {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            border-bottom-color: #334155;
        }

        .dark-mode .department-item {
            background: #334155;
            border-color: #475569;
            color: #e2e8f0;
        }

        .dark-mode .department-item:hover {
            background: #475569;
            border-color: #3b82f6;
        }

        .dark-mode .department-item.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: #ffffff;
            border-color: #1d4ed8;
        }

        /* Responzivní design */
        @media (max-width: 768px) {
            .departments-panel-directory {
                width: 100%;
                height: auto;
                position: relative;
                top: 0;
                left: 0;
            }

            .employee-content {
                margin-left: 0;
                padding: 0 1rem;
            }

            .departments-list {
                height: auto;
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                padding: 1rem;
            }

            .department-item {
                margin-bottom: 0;
                flex: 1;
                min-width: 150px;
            }
        }

        .main-flex {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: flex-start;
            width: 100vw;
            max-width: none;
            margin: 2rem 0 2rem 0;
            gap: 48px;
        }

        /* Styly pro zaměstnance - bez hierarchického řazení */
        .employee.manager {
            position: relative;
            /* Odstraněno: order: -1; - všichni zaměstnanci budou řazeni abecedně */
        }

        /* Aktualizované styly pro štítky v kartách - sjednocené s modálním oknem */
        .hierarchy-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 0.4rem 0.6rem;
            border-radius: 16px;
            font-size: 0.7rem;
            font-weight: 600;
            z-index: 5;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            gap: 0.3rem;
            transition: all 0.3s ease;
        }

        .hierarchy-badge.predstavenstvo {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .hierarchy-badge.vedouci {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }

        .hierarchy-badge i {
            font-size: 0.65rem;
        }

        .dark-mode .hierarchy-badge {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .dark-mode .hierarchy-badge.predstavenstvo {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }

        .dark-mode .modal-badge {
            border-color: rgba(255, 255, 255, 0.1);
        }

        .dark-mode .modal-content {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .dark-mode .info-card {
            background: rgba(30, 41, 59, 0.7);
            border-color: rgba(59, 130, 246, 0.3);
            color: #e2e8f0;
        }

        .dark-mode .contact-item {
            background: rgba(30, 41, 59, 0.6);
            border-color: rgba(16, 185, 129, 0.3);
            color: #e2e8f0;
        }

        /* Vylepšené modální okno - moderní design */
        .modal-content {
            background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            box-shadow:
                0 25px 60px rgba(0,0,0,0.15),
                0 8px 25px rgba(59, 130, 246, 0.1),
                inset 0 1px 0 rgba(255,255,255,0.8);
            padding: 2rem;
            max-width: 500px;
            width: 90%;
        }

        .modal-content h2 {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            flex-wrap: wrap;
            margin-bottom: 1.5rem;
            font-size: 1.75rem;
            color: #1e293b;
        }

        /* Jednotné styly pro štítky - použité v obou stránkách */
        .modal-badge, .hierarchy-badge {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 16px;
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.4rem;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .modal-badge.predstavenstvo, .hierarchy-badge.predstavenstvo {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .modal-badge.vedouci, .hierarchy-badge.vedouci {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
        }

        /* Nový layout modálního okna */
        .modal-header-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .modal-header-section img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 1rem;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
            border: 3px solid rgba(255, 255, 255, 0.9);
        }

        .modal-info-cards {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .info-card {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.7);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .info-card:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .info-card i {
            color: #3b82f6;
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
        }

        .modal-contact-section {
            margin-top: 1.5rem;
        }

        .contact-grid {
            display: grid;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            background: rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 10px;
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            background: rgba(255, 255, 255, 0.8);
            border-color: rgba(16, 185, 129, 0.4);
        }

        .contact-item i {
            color: #10b981;
            font-size: 1rem;
            width: 18px;
            text-align: center;
        }

        .modal-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
            flex-wrap: wrap;
            justify-content: center;
        }

        .modal-action-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal-action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .modal-action-btn:hover::before {
            left: 100%;
        }

        .modal-action-btn.map-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .modal-action-btn.map-btn:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
            transform: translateY(-2px);
        }

        .main-flex-custom {
            flex-direction: column;
            gap: 32px;
            align-items: stretch;
        }

        .employee-bar {
            width: 100%;
            margin: 0 0 1.5rem 0;
            background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
            box-shadow:
                0 8px 24px rgba(37,99,235,0.06),
                0 4px 8px rgba(37,99,235,0.04),
                inset 0 1px 0 rgba(255,255,255,0.8);
            border-radius: 20px;
            border: 1px solid rgba(37,99,235,0.1);
            padding: 1.25rem 1.5rem;
            position: relative;
            z-index: 10;
            backdrop-filter: blur(10px);
            height: auto;
            min-height: 120px;
        }

        /* Zjednodušené styly pro vyhledávací lištu */
        .search-container-simple {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.15);
            border-radius: 16px;
            margin: 1.5rem 0;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;
        }

        .dark-mode .search-container-simple {
            background: rgba(30, 41, 59, 0.95);
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .search-input-wrapper {
            position: relative;
            margin-bottom: 1rem;
        }

        .search-input-simple {
            width: 100%;
            padding: 1rem 3rem 1rem 3rem;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 12px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            outline: none;
            color: #1f2937;
            font-weight: 500;
        }

        .search-input-simple:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: rgba(255, 255, 255, 1);
        }

        .dark-mode .search-input-simple {
            background: rgba(30, 41, 59, 0.9);
            border-color: rgba(59, 130, 246, 0.3);
            color: #e2e8f0;
        }

        .dark-mode .search-input-simple:focus {
            background: rgba(30, 41, 59, 1);
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }

        .map-search-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.15);
            border-radius: 16px;
            margin: 1rem;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .dark-mode .map-search-header {
            background: rgba(30, 41, 59, 0.95);
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }



        .department-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 16px;
            font-weight: 600;
            font-size: 1rem;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            min-width: 200px;
            justify-content: center;
            white-space: nowrap;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .dark-mode .department-title {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
            box-shadow: 0 4px 12px rgba(30, 64, 175, 0.4);
        }

        /* Název oddělení v adresáři */
        .directory-department-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            border-radius: 16px;
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .dark-mode .directory-department-title {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
            box-shadow: 0 4px 16px rgba(30, 64, 175, 0.4);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .department-title i {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .department-title i, .directory-department-title i {
            font-size: 1rem;
            opacity: 0.9;
        }

        .search-results-count {
            color: #6b7280;
            font-size: 0.9rem;
            font-weight: 500;
            min-width: 200px;
            text-align: right;
            flex-shrink: 0;
            background: rgba(59, 130, 246, 0.08);
            padding: 0.5rem 1rem;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .dark-mode .search-results-count {
            background: rgba(30, 64, 175, 0.2);
            color: #cbd5e1;
        }

        /* Styly pro search-icon a search-clear-btn v zjednodušené verzi */
        .search-input-wrapper .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
            font-size: 1rem;
            z-index: 2;
            pointer-events: none;
        }

        .dark-mode .search-input-wrapper .search-icon {
            color: #9ca3af;
        }

        .search-input-wrapper .search-clear-btn {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            z-index: 2;
        }

        .search-input-wrapper .search-clear-btn:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .dark-mode .search-input-wrapper .search-clear-btn {
            color: #9ca3af;
        }

        .dark-mode .search-input-wrapper .search-clear-btn:hover {
            background: rgba(59, 130, 246, 0.2);
            color: #60a5fa;
        }



        /* Styly pro search-suggestions s dark mode */
        .search-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 16px;
            margin-top: 0.5rem;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.15);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
            display: none;
        }

        .search-suggestions.show {
            display: block;
        }

        .dark-mode .search-suggestions {
            background: rgba(30, 41, 59, 0.98);
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        }

        .suggestion-item {
            padding: 1rem 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(59, 130, 246, 0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .suggestion-item:hover {
            background: rgba(59, 130, 246, 0.1);
        }

        .dark-mode .suggestion-item {
            border-bottom-color: rgba(59, 130, 246, 0.2);
            color: #e2e8f0;
        }

        .dark-mode .suggestion-item:hover {
            background: rgba(59, 130, 246, 0.2);
        }

        .suggestion-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid rgba(59, 130, 246, 0.3);
        }

        .suggestion-info {
            flex: 1;
        }

        .suggestion-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }

        .dark-mode .suggestion-name {
            color: #f1f5f9;
        }

        .suggestion-details {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .dark-mode .suggestion-details {
            color: #94a3b8;
        }

        /* Dark mode pro employee-content a další prvky */
        .dark-mode .employee-content {
            background: rgba(15, 23, 42, 0.5);
        }

        .dark-mode .filter-header {
            background: rgba(30, 41, 59, 0.8);
            color: #e2e8f0;
        }

        .dark-mode .employee-count {
            color: #cbd5e1;
        }

        /* Dark mode pro employee-bar v mapě */
        .dark-mode .employee-bar {
            background: rgba(15, 23, 42, 0.9);
            border-bottom-color: rgba(59, 130, 246, 0.3);
        }

        .dark-mode .employee-list-horizontal {
            background: rgba(30, 41, 59, 0.8);
        }

        /* Dark mode pro departments panel */
        .dark-mode .departments-panel,
        .dark-mode .departments-panel-directory {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            border-right-color: rgba(59, 130, 246, 0.3);
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
        }

        .dark-mode .departments-panel .panel-header,
        .dark-mode .departments-panel-directory .panel-header {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
            border-bottom-color: rgba(59, 130, 246, 0.4);
        }

        /* Dark mode pro error message */
        .dark-mode .error-message {
            background: rgba(30, 41, 59, 0.9);
            color: #f87171;
            border-color: rgba(239, 68, 68, 0.3);
        }

        /* Dark mode pro back-to-top button */
        .dark-mode #backToTopBtn {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
        }

        .dark-mode #backToTopBtn:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
        }

        /* Dark mode pro vyhledávací input a suggestions - kompletní implementace */
        .dark-mode .main-search-input::placeholder {
            color: #9ca3af;
        }

        .dark-mode .search-suggestions::-webkit-scrollbar {
            width: 8px;
        }

        .dark-mode .search-suggestions::-webkit-scrollbar-track {
            background: rgba(30, 41, 59, 0.5);
            border-radius: 4px;
        }

        .dark-mode .search-suggestions::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.5);
            border-radius: 4px;
        }

        .dark-mode .search-suggestions::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.7);
        }

        /* Dark mode pro employee listing */
        .dark-mode .employee_listing {
            background: transparent;
        }

        /* Dark mode pro footer */
        .dark-mode #Footer {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            color: #cbd5e1;
            border-top: 1px solid rgba(59, 130, 246, 0.3);
        }

        /* Dark mode pro office-map-container */
        .dark-mode .office-map-container {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        /* Dark mode pro employee-list-horizontal */
        .dark-mode .employee-list-horizontal li {
            background: rgba(30, 41, 59, 0.8);
            border-color: rgba(59, 130, 246, 0.3);
            color: #e2e8f0;
        }

        .dark-mode .employee-list-horizontal li:hover {
            background: rgba(59, 130, 246, 0.2);
            border-color: #3b82f6;
        }

        .dark-mode .employee-list-horizontal li.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            border-color: #3b82f6;
            color: white;
        }

        /* Responzivní úpravy pro zjednodušenou strukturu */
        @media (max-width: 768px) {
            .search-container-simple {
                margin: 1rem 0;
                padding: 1rem;
            }

            .map-search-header {
                margin: 0.5rem;
                padding: 1rem;
                flex-direction: column;
                gap: 0.75rem;
            }

            .directory-department-title {
                text-align: center;
                justify-content: center;
                margin-bottom: 1rem;
            }

            .department-title {
                min-width: auto;
                width: 100%;
                justify-content: center;
                margin-bottom: 0.5rem;
            }

            .search-results-count {
                text-align: center;
                margin-top: 0.5rem;
            }
        }

        @media (max-width: 480px) {
            .search-input-simple {
                padding: 0.75rem 2.5rem 0.75rem 2.5rem;
                font-size: 0.9rem;
            }

            .search-input-wrapper .search-icon {
                left: 0.75rem;
            }

            .search-input-wrapper .search-clear-btn {
                right: 0.75rem;
            }
        }

        /* Přepsání CSS pro filter-header v adresáři */
        .employee-content .filter-header {
            display: block !important;
            justify-content: flex-start !important;
            margin-bottom: 0 !important;
        }

        .search-container {
            position: relative;
            flex: 1;
            max-width: 400px;
            flex-shrink: 0;
        }



        .search-box-horizontal {
            width: 100%;
            padding: 1rem 3rem 1rem 1.5rem;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 16px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            outline: none;
            color: #1f2937;
            font-weight: 500;
        }

        .search-box-horizontal:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: rgba(255, 255, 255, 0.95);
        }

        .search-clear-btn {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            opacity: 0;
            pointer-events: none;
        }

        .search-clear-btn.visible {
            opacity: 1;
            pointer-events: auto;
        }

        .search-clear-btn:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .search-results-count {
            color: #6b7280;
            font-size: 0.9rem;
            font-weight: 500;
            min-width: 200px;
            text-align: right;
            background: rgba(37,99,235,0.08);
            border-radius: 8px;
            margin-left: 0.5rem;
            white-space: nowrap;
            transition: all 0.3s ease;
        }

        .search-results-count.visible {
            display: flex;
            align-items: center;
        }

        /* Search hint and not found messages */
        .search-hint, .not-found-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            padding: 1rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 0.95rem;
            z-index: 10001;
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
        }

        .search-hint {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .not-found-message {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .search-hint.show, .not-found-message.show {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }

        .search-hint i, .not-found-message i {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* Dark mode pro název oddělení */
        .dark-mode .department-title {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            border: 1px solid #475569;
            color: #e2e8f0;
        }

        .dark-mode .directory-department-title {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            border: 1px solid #475569;
            color: #e2e8f0;
            box-shadow: 0 4px 16px rgba(30, 41, 59, 0.4);
        }

        /* Responzivní design pro název oddělení */
        @media (max-width: 768px) {
            .department-title {
                min-width: 120px;
                font-size: 0.75rem;
                padding: 0.4rem 0.8rem;
            }

            .department-title span {
                display: none;
            }

            .department-title::after {
                content: attr(data-short);
            }

            .directory-department-title {
                font-size: 0.95rem;
                padding: 0.75rem 1rem;
                margin-bottom: 1rem;
            }

            .directory-department-title i {
                font-size: 1rem;
            }
        }

        /* Skrytí tlačítka "back to top" ve vnořené mapě */
        body.map-view #backToTopBtn {
            display: none;
        }
    </style>


</head>

<body>

    <header id="headerNav">
        <div class="navbar">
            <div class="logo">
                <a href="index.html"><img src="img/logo1.svg" alt="Logo OTE" class="logo1"></a>
            </div>
            <div class="header-content">
                <h1 class="header-title" id="pageTitle">Adresář OTE</h1>
                <div class="view-toggle-header">
                    <button id="employeeListBtn" class="view-toggle-btn active" data-view="employees" onclick="switchToEmployees()">
                        <i class="fas fa-users"></i>
                        <span>Adresář</span>
                    </button>
                    <button id="officeMapBtn" class="view-toggle-btn" data-view="map" onclick="switchToMap()">
                        <i class="fas fa-map-marked-alt"></i>
                        <span>Mapa</span>
                    </button>
                </div>
            </div>
            <div class="header-actions">
                <button class="theme-toggle-btn" id="themeToggle" title="Přepnout režim">
                    <i class="fas fa-sun" id="themeIcon"></i>
                </button>
            </div>
        </div>
    </header>



    <!-- Adresář OTE -->
    <section class="employee_directory" id="employeeSection">
        <div class="employee-directory-container">
            <!-- Levý panel s oddělením -->
            <div class="departments-panel-directory" id="departmentsPanelDirectory">
                <div class="panel-header">
                    <h3><i class="fas fa-building"></i> Oddělení</h3>
                </div>
                <div class="departments-list" id="departmentsListDirectory">
                    <!-- Oddělení se načtou dynamicky -->
                </div>
            </div>

            <!-- Hlavní obsah s kartami zaměstnanců -->
            <div class="employee-content">
                <!-- Štítek aktuálního oddělení -->
                <div class="directory-department-title" id="directoryDepartmentTitle">
                    <i class="fas fa-building"></i>
                    <span id="directoryDepartmentName">Všichni zaměstnanci</span>
                </div>

                <!-- Vyhledávací lišta s našeptáváním -->
                <div class="search-container-simple">
                    <div class="search-input-wrapper">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="directorySearchInput" placeholder="Vyhledat zaměstnance..." class="search-input-simple">
                        <button type="button" class="search-clear-btn" id="directorySearchClearBtn" style="display: none;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div id="directory-search-suggestions" class="search-suggestions"></div>
                    <div class="search-results-count" id="directorySearchResultsCount">
                        Zobrazeno: <span id="directoryVisibleCount">0</span> z <span id="directoryTotalCount">0</span> zaměstnanců
                    </div>
                </div>

                <div class="filter-header">
                </div>

                <div class="employee_listing" id="employee-grid">
                </div>
            </div>
        </div>
    </section>

    <!-- Mapa rozmístění pracovišť - 1:1 kopie z mapa.html -->
    <section class="office_map_section" id="mapSection" style="display: none;">
        <div class="main-flex main-flex-custom">
            <div class="employee-bar">
                <!-- Vyhledávací lišta s našeptáváním pro mapu -->
                <div class="map-search-header">
                    <div class="department-title" id="mapDepartmentTitle">
                        <i class="fas fa-building"></i>
                        <span id="mapDepartmentName">Všichni zaměstnanci</span>
                    </div>
                    <div class="search-container-simple">
                        <div class="search-input-wrapper">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" id="mapSearchInput" placeholder="Vyhledat zaměstnance..." class="search-input-simple">
                            <button type="button" class="search-clear-btn" id="mapSearchClearBtn" style="display: none;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div id="map-search-suggestions" class="search-suggestions"></div>
                    </div>
                    <div class="search-results-count" id="mapSearchResultsCount">
                        <span id="mapResultsCountText">0 výsledků</span>
                    </div>
                    <ul class="employee-list-horizontal" id="mapEmployeeList"></ul>
                </div>
            </div>

            <div class="mapa-wrapper mapa-wrapper-custom">
                <!-- Levý panel s oddělením -->
                <div class="departments-panel" id="departmentsPanel">
                    <div class="panel-header">
                        <h3><i class="fas fa-building"></i> Oddělení</h3>
                    </div>
                    <div class="departments-list" id="departmentsList">
                        <!-- Dynamicky generované oddělení -->
                    </div>
                </div>

                <div class="office-map-container" id="mapContainer">
                    <img id="office-map-img" src="img/Greenline.png" alt="Plán kanceláře">
                </div>
            </div>
        </div>
        <div id="map-error" class="error-message">Obrázek plánu kanceláře se nepodařilo načíst. Zkontrolujte cestu k souboru <b>img/Greenline.png</b> nebo kontaktujte správce.</div>
    </section>

    <footer id="Footer">
        <p>Poslední aktualizace: <span id="lastUpdated"></span></p>
    </footer>

    <button id="backToTopBtn" title="Zpět nahoru">
        <i class="fas fa-chevron-up"></i>
        <span>Nahoru</span>
    </button>

    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>

            <!-- Hlavička s fotkou a jménem -->
            <div class="modal-header-section">
                <img id="modalImage" src="" alt="Employee Image">
                <h2 id="modalName"></h2>
            </div>

            <!-- Informace o pozici a oddělení -->
            <div class="modal-info-cards">
                <div class="info-card" id="modalPosition">
                    <i class="fas fa-briefcase"></i>
                    <span>Pracovní pozice: Specialista</span>
                </div>
                <div class="info-card" id="modalDepartment">
                    <i class="fas fa-building"></i>
                    <span>Oddělení: Smluvní vztahy a povolenky</span>
                </div>
            </div>

            <!-- Tlačítko pro mapu -->
            <div id="modalOffice"></div>

            <!-- Kontaktní informace -->
            <div class="modal-contact-section">
                <div class="contact-grid">
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span id="modalPhone">Telefon: +420 234 686 370</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-mobile-alt"></i>
                        <span id="modalMobile">Mobil: +420 603 568 443</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span id="modalEmail">Email: <EMAIL></span>
                    </div>
                </div>

                <!-- Akční tlačítka -->
                <div id="modalTeams"></div>
            </div>
        </div>
    </div>

    <script src="js/navbar.js?v=8"></script>
    <script src="js/script.js?v=8"></script>
    <script src="js/back-to-top.js?v=8"></script>
    <script src="js/update-date.js?v=8"></script>
    <script src="js/map-integration.js?v=8"></script>

    <script>
        document.addEventListener('contextmenu', function (e) {
            e.preventDefault();
        });
    </script>

</body>

</html>
